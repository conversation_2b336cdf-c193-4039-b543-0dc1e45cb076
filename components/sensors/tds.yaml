one_wire:
    - platform: gpio
      pin: GPIO6

sensor:
    - platform: dallas_temp
      id: water_temperature
      name: 'Water Temperature'
      update_interval: 1s

    - platform: adc
      id: water_tds_voltage
      pin: GPIO3
      internal: true
      update_interval: 1s

    - platform: template
      id: water_temperature_correction
      update_interval: 1s
      internal: true
      lambda: 'return (1.0 + (0.02 * (id(water_temperature).state - 25.0)));'

    - platform: template
      id: water_tds_voltage_correction
      update_interval: 1s
      lambda: 'id(water_temperature_correction).state * id(water_tds_voltage).state);'

#pragma once
#include "esphome/core/macros.h"
#define ESPHOME_BOARD "esp-wrover-kit"
#define ESPHOME_COMPONENT_COUNT 16
#define ESPHOME_ENTITY_BUTTON_COUNT 2
#define ESPHOME_ENTITY_SENSOR_COUNT 1
#define ESPHOME_ENTITY_TEXT_SENSOR_COUNT 4
#define ESPHOME_THREAD_MULTI_ATOMICS
#define ESPHOME_VARIANT "ESP32"
#define USE_API
#define USE_API_PLAINTEXT
#define USE_BUTTON
#define USE_ENTITY_ICON
#define USE_ESPHOME_TASK_LOG_BUFFER
#define USE_ESP_IDF_VERSION_CODE VERSION_CODE(5, 4, 2)
#define USE_ETHERNET
#define USE_JSON
#define USE_LOGGER
#define USE_MD5
#define USE_MDNS
#define USE_NETWORK
#define USE_NETWORK_IPV6 false
#define USE_OTA
#define USE_OTA_VERSION 2
#define USE_SENSOR
#define USE_SOCKET_IMPL_BSD_SOCKETS
#define USE_SOCKET_SELECT_SUPPORT
#define USE_TEXT_SENSOR
#define USE_WEBSERVER
#define USE_WEBSERVER_PORT 80
#define USE_WEBSERVER_PRIVATE_NETWORK_ACCESS
#define USE_WEBSERVER_VERSION 2
